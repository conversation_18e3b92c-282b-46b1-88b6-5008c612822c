import os
import json
from typing import Sequence
from pydantic import BaseModel, Field, ValidationError
from src.domain.entities.flashcard import Flashcard
from src.application.ports.flashcard_generator import FlashcardGenerator

import google.generativeai as genai

_SYSTEM_INSTRUCTIONS = """You are a flashcard generator.
Given study text, return flashcards as a JSON array:
[{ "question": "...", "answer": "..." }, ...].
Return ONLY JSON, no extra text."""

class _FlashcardItem(BaseModel):
    question: str = Field(..., min_length=1)
    answer: str = Field(..., min_length=1)

class GeminiFlashcardGenerator(FlashcardGenerator):
    def __init__(self, model_name: str = "gemini-1.5-flash", temperature: float = 0.2):
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise RuntimeError("GEMINI_API_KEY is missing")
        genai.configure(api_key=api_key)
        self._model = genai.GenerativeModel(model_name)
        self._temperature = temperature

    async def generate(
        self,
        source_text: str,
        *,
        language: str = "en",
        max_cards: int = 20,
        style: str | None = None
    ) -> Sequence[Flashcard]:
        prompt = f"""{_SYSTEM_INSTRUCTIONS}
Constraints:
- Language: {language}
- Max cards: {max_cards}
- Style: {style or "concise"}

Source:
\"\"\"{source_text[:12000]}\"\"\"
"""
        response = self._model.generate_content(prompt, generation_config={"temperature": self._temperature})
        text = (response.text or "").strip()

        if text.startswith("```"):
            text = text.strip("`")
            idx = text.find("[")
            if idx != -1:
                text = text[idx:]

        try:
            raw = json.loads(text)
            items = [_FlashcardItem.model_validate(obj) for obj in raw]
        except (json.JSONDecodeError, ValidationError):
            return []

        return [Flashcard(question=i.question, answer=i.answer) for i in items[:max_cards]]
