from typing import Sequence
from src.application.dto.generate_flashcards_input import GenerateFlashcardsInput
from src.domain.entities.flashcard import Flashcard
from src.application.ports.pdf_text_extractor import PDFTextExtractor
from src.application.ports.flashcard_generator import FlashcardGenerator

class GenerateFlashcardsFromPDF:
    def __init__(self, extractor: PDFTextExtractor, generator: FlashcardGenerator):
        self._extractor = extractor
        self._generator = generator

    async def execute(
        self,
        *, file_bytes: bytes, params: GenerateFlashcardsInput
    ) -> Sequence[Flashcard]:
        text = await self._extractor.extract_text(file_bytes)
        if not text or len(text.strip()) < 50:
            return []
        return await self._generator.generate(
            text,
            language=params.language,
            max_cards=params.max_cards,
            style=params.style,
        )
