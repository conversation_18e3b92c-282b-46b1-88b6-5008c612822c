from dishka import AsyncContainer, make_async_container
from dishka.integrations.fastapi import FastapiProvider

from src.main.config import Config
from src.main.ioc.providers.core import ConfigProvider
from src.main.ioc.providers.interactor import InteractorProvider
from src.main.ioc.providers.service import ServiceProvider


def create_container(config: Config) -> AsyncContainer:
    return make_async_container(
        FastapiProvider(),
        ConfigProvider(),
        ServiceProvider(),
        InteractorProvider(),
        context={Config: config},
    )
