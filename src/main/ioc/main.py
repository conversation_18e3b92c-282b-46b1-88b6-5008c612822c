from dishka import AsyncContainer, make_async_container
from dishka.integrations.fastapi import FastapiProvider

from src.main.config import Config
from src.main.ioc.providers.container import ContainerProvider
from src.main.ioc.providers.core import ConfigProvider


def create_container(config: Config) -> AsyncContainer:
    return make_async_container(
        FastapiProvider(),
        ConfigProvider(),
        ContainerProvider(),
        context={Config: config},
    )
