from dishka import Provider, provide, Scope
from src.application.use_cases.generate_flashcard_from_document import (
    GenerateFlashcardsFromPDF,
)
from src.infrastructure.adapters.pymupdf_text_extractor import PyMuPDFTextExtractor
from src.infrastructure.adapters.gemini_flashcard_generator import (
    GeminiFlashcardGenerator,
)


class ContainerProvider(Provider):
    @provide(scope=Scope.APP)
    def pdf_text_extractor(self) -> PyMuPDFTextExtractor:
        return PyMuPDFTextExtractor()

    @provide(scope=Scope.APP)
    def flashcard_generator(self) -> GeminiFlashcardGenerator:
        return GeminiFlashcardGenerator()

    @provide(scope=Scope.REQUEST)
    def generate_flashcards_from_pdf(
        self, extractor: PyMuPDFTextExtractor, generator: GeminiFlashcardGenerator
    ) -> GenerateFlashcardsFromPDF:
        return GenerateFlashcardsFromPDF(extractor, generator)
