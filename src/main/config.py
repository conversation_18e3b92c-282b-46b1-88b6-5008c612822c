from typing import Literal

from pydantic import BaseModel
from pydantic_settings import BaseSettings as _BaseSettings
from pydantic_settings import SettingsConfigDict


class BaseSettings(_BaseSettings):
    model_config = SettingsConfigDict(
        extra="ignore",
        env_file=".env",
        env_file_encoding="utf-8",
    )


class ApplicationConfig(BaseSettings, env_prefix="APPLICATION_"):
    title: str
    debug: bool = False


class SessionConfig(BaseSettings, env_prefix="SESSION_"):
    lifetime_minutes: int

    cookie_name: str = "session_id"
    samesite: Literal["lax", "strict", "none"] = "lax"
    path: str = "/"
    secure: bool = True
    domain: str | None = None


class Config(BaseModel):
    app: ApplicationConfig
    session: SessionConfig


def create_config() -> Config:
    return Config(app=ApplicationConfig(), session=SessionConfig())
