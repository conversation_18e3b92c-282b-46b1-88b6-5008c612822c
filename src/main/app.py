from dishka.integrations.fastapi import setup_dishka
from fastapi import Fast<PERSON><PERSON>
from starlette.middleware.base import BaseHTTPMiddleware

from src.main.config import create_config, Config
from src.main.ioc.main import create_container
from src.presentation.controllers import auth, main
from src.presentation.exceptions import register_exception_handlers
from src.presentation.middlewares.session import SessionMiddleware

from dishka import AsyncContainer


def setup_routers(app: FastAPI) -> None:
    app.include_router(main.router)
    app.include_router(auth.router)


def setup_middlewares(app: FastAPI, config: Config) -> None:
    app.add_middleware(
        BaseHTTPMiddleware, SessionMiddleware(session_config=config.session)
    )


def create_application() -> FastAPI:
    config: Config = create_config()
    app: FastAPI = FastAPI(title=config.app.title, debug=config.app.debug)

    container: AsyncContainer = create_container(config)
    setup_dishka(container, app)

    setup_routers(app)
    setup_middlewares(app, config)
    register_exception_handlers(app)

    return app
