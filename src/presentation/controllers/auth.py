from dishka import FromDishka
from dishka.integrations.fastapi import inject
from fastapi import APIRouter
from starlette import status

from src.application.interactors.register_user import (
    RegisterUserInteractor,
    RegisterUserRequest,
    RegisterUserResponse,
)

router = APIRouter(prefix="/auth", tags=["auth"])


@router.post(
    "/register",
    status_code=status.HTTP_201_CREATED,
    responses={
        409: {"description": "User already exists"},
    },
)
@inject
async def register(
    data: RegisterUserRequest,
    register_user: FromDishka[RegisterUserInteractor],
) -> RegisterUserResponse:
    return await register_user(data)
