from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from typing import List
from dishka.integrations.fastapi import FromDishka, inject

from src.application.use_cases.generate_flashcard_from_document import (
    GenerateFlashcardsFromPDF,
)
from src.application.dto.generate_flashcards_input import GenerateFlashcardsInput
from src.presentation.schemas.flashcards import FlashcardOut


router = APIRouter()


@router.post("/pdf", response_model=List[FlashcardOut])
@inject
async def generate_flashcards_from_pdf(
    use_case: FromDishka[GenerateFlashcardsFromPDF],
    file: UploadFile = File(...),
    language: str = Form("en"),
    max_cards: int = Form(20),
    style: str | None = Form(None),
) -> List[FlashcardOut]:
    if file.content_type not in ("application/pdf", "application/octet-stream"):
        raise HTTPException(status_code=400, detail="Only PDF files are supported")
    file_bytes = await file.read()

    input_dto = GenerateFlashcardsInput(
        language=language,
        max_cards=max_cards,
        style=style,
    )

    cards = await use_case.execute(file_bytes=file_bytes, params=input_dto)
    return [FlashcardOut(question=c.question, answer=c.answer) for c in cards]
