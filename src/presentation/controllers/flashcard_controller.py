from fastapi import APIRouter, UploadFile, File, HTTPException
from typing import List
from dishka.integrations.fastapi import FromDishka

from src.application.use_cases.generate_flashcard_from_document import GenerateFlashcardsFromPDF
from src.presentation.schemas.flashcards import FlashcardOut, GenerateFlashcardsQuery


router = APIRouter(tags=["flashcards"])

@router.post("/flashcards/pdf", response_model=List[FlashcardOut])
async def generate_flashcards_from_pdf(
    params: GenerateFlashcardsQuery,
    file: UploadFile = File(...),
    use_case: FromDishka[GenerateFlashcardsFromPDF] = None,
):
    if file.content_type not in ("application/pdf", "application/octet-stream"):
        raise HTTPException(status_code=400, detail="Only PDF files are supported")
    file_bytes = await file.read()
    cards = await use_case.execute(file_bytes=file_bytes, params=params)
    return [FlashcardOut(question=c.question, answer=c.answer) for c in cards]
