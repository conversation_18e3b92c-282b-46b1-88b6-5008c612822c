[tool.poetry]
name = "Quizzfly AI"
version = "0.1.0"
description = ""
authors = ["pdhuy"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.115.5"
pydantic-settings = "^2.6.1"
dishka = "^1.4.1"
uvicorn = "^0.34.0"
sqlalchemy = { extras = ["asyncio"], version = "^2.0.37" }
asyncpg = "^0.30.0"
bcrypt = "^4.2.1"
alembic = "^1.14.1"
pydantic = { extras = ["email"], version = "^2.10.6" }
google-generativeai = "^0.8.5"
pymupdf = "^1.26.3"

[tool.poetry.group.dev.dependencies]
mypy = "^1.13.0"
ruff = "^0.8.0"
pre-commit = "^4.0.1"
pytest = "^8.0.0"
pytest-cov = "^6.0.0"
pytest-asyncio = "^0.25.0"
poethepoet = "^0.29.0"

[tool.mypy]
plugins = ["pydantic.mypy"]
strict = true
follow_imports = "normal"
show_error_context = false
pretty = true
ignore_missing_imports = true

[tool.ruff]
target-version = "py313"
exclude = ["migrations"]

[tool.ruff.lint]
select = [
    "ALL"
]
ignore = [
    "RUF001", "RUF002", "RUF003", "RUF012",
    "COM812", "ISC001",
    "D",
    "RET502", "RET503",
    "ANN401",
    "I001", "INP001",
    "FBT001", "FBT002",
    "TRY003", "EM101",
    "PLR0913",
    "TC001", "TC002"
]

[tool.poe.tasks]
# Code quality tasks
ruff-check = "ruff check ."
ruff-format = "ruff format ."
ruff-format-check = "ruff format --check ."
mypy-check = "mypy src"

# Combined code quality checks
lint = ["ruff-check", "mypy-check"]
format = "ruff format ."
format-check = "ruff format --check ."

# For backward compatibility with CI
black-check = "ruff format --check ."

# Test tasks
test-unit = "pytest tests/ -v --cov=src --cov-report=html --cov-report=term-missing"
test = "test-unit"

# Combined tasks
check-all = ["lint", "format-check", "test-unit"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[tool.coverage.run]
source = ["src"]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
