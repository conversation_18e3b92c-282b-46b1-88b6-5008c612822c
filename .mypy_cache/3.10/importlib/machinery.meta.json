{"data_mtime": 1755745017, "dep_lines": [2, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["importlib._bootstrap", "importlib._bootstrap_external", "sys", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "a306f9942e253d05433d97fbf1113fd4f35bdb58", "id": "importlib.machinery", "ignore_all": true, "interface_hash": "4fbc63a6b738ec23f4e3b33a9fc2a031dfff9cd1", "mtime": 1755516231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": ["pydantic.mypy"], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/home/<USER>/.cache/pypoetry/virtualenvs/quizzfly_ai-bgQoNlRY-py3.10/lib/python3.10/site-packages/mypy/typeshed/stdlib/importlib/machinery.pyi", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 839, "suppressed": [], "version_id": "1.15.0"}