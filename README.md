# Quizzfly AI

## 🏗️ Project Structure

```
src/
├── application/          # Application business rules
│   ├── interactors/      # Use cases implementation
│   ├── interfaces/       # Abstract interfaces (ports)
│   └── validators.py     # Validation rules
├── domain/               # Enterprise business rules
│   ├── entities/         # Business entities
│   └── exceptions.py     # Domain exceptions
├── infrastructure/       # External frameworks and tools
│   ├── adapters/         # Implementation of interfaces (adapters)
│   └── database/         # Database related code (SQLAlchemy)
├── main/                 # Application configuration
│   ├── ioc/              # Dependency injection setup
│   └── config.py         # Configuration management
└── presentation/         # Controllers and exception handlers
    └── controllers/      # API endpoints
```

## 🚀 Getting Started

### Prerequisites

- Python 3.10+
- Poetry
- Docker and Docker Compose (optional)

### Installation

1. Clone the repository:

```bash
<NAME_EMAIL>:Quizzfly/quizzfly-ai.git
cd quizzfly-ai
```

2. Install dependencies:

```bash
poetry install
```

3. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Run migrations:

```bash
poetry run alembic upgrade head
```

### Running the Application

#### Using Poetry:

```bash
poetry run uvicorn src.main.app:create_application --factory
```

#### Using Docker:

```bash
docker-compose up -d
```

## 📖 API Documentation

Once the application is running, you can access:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 🔧 Configuration

Configuration is managed through environment variables and pydantic settings. Key configuration options:

- `APPLICATION_TITLE`: Application name
- `APPLICATION_DEBUG`: Debug mode flag
- `SESSION_*`: Session settings