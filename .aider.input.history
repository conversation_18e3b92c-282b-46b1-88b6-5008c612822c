
# 2025-08-19 14:56:22.807522
+yes

# 2025-08-19 14:56:25.605452
+clear

# 2025-08-19 16:49:34.130175
+why fastapi.exceptions.FastAPIError: Invalid args for response field! Hint: check that <class 'src.domain.ports.pdf_processor.PdfProcessorPort'> is a valid Pydantic field type. If you are using a return type annotation that is not a valid Pydantic field (e.g. Union[Response, dict, None]) you can disable generating the response model from the type annotation with the path operation decorator parameter response_model=None. Read more: https://fastapi.tiangolo.com/tutorial/response-model/   please check my codebase and fix it

# 2025-08-19 16:49:39.003193
+yes

# 2025-08-19 16:49:45.150850
+no

# 2025-08-19 16:50:07.772171
+why fastapi.exceptions.FastAPIError: Invalid args for response field! Hint: check that <class 'src.domain.ports.pdf_processor.PdfProcessorPort'> is a valid Pydantic field type. If you are using a return type annotation that is not a valid Pydantic field (e.g. Union[Response, dict, None]) you can disable generating the response model from the type annotation with the path operation decorator parameter response_model=None.   please check my codebase and fix it

# 2025-08-19 16:51:29.938380
+from abc import ABC, abstractmethod
+from ..value_objects.pdf_content import PdfContent
+
+class PdfProcessorPort(ABC):
+    @abstractmethod
+    async def extract_text(self, file_content: bytes) -> PdfContent:
+        pass

# 2025-08-19 16:51:54.851688
+from dishka import Container
+from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
+from starlette import status
+
+from src.application.commands.create_flashcards import CreateFlashcardsCommand, CreateFlashcardsInteractor
+from src.application.exceptions import ApplicationError
+from src.infrastructure.exceptions import InfrastructureError
+from ..exceptions import map_exception
+
+router = APIRouter(prefix="/flashcards", tags=["flashcard"])
+
+@router.post(
+    "",
+    status_code=status.HTTP_201_CREATED,
+    responses={
+        400: {"description": "Invalid input parameters"},
+        422: {"description": "Invalid file format or processing error"},
+    },
+)
+async def create_flashcards(
+    pdf_file: UploadFile = File(...),
+    number_of_questions: int = Form(..., gt=0),
+    interactor: CreateFlashcardsInteractor = Depends()
+) -> dict:
+    if not pdf_file.filename.endswith('.pdf'):
+        raise HTTPException(
+            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
+            detail="Only PDF files are allowed"
+        )
+
+    try:
+        file_content = await pdf_file.read()
+        command = CreateFlashcardsCommand(
+            file_content=file_content,
+            number_of_questions=number_of_questions
+        )
+        result = await interactor.execute(command)
+
+        return {
+            "message": "Flashcards generated successfully",
+            "flashcards": [
+                {"question": f.question, "answer": f.answer}
+                for f in result.flashcards
+            ],
+            "total": result.total
+        }
+    except (ApplicationError, InfrastructureError) as e:
+        raise map_exception(e)
