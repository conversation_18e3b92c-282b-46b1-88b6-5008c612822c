"""Sample test file to ensure CI works."""

import pytest


def test_sample() -> None:
    """Sample test to verify testing setup works."""
    assert True  # noqa: S101


def test_with_fixture(sample_fixture: str) -> None:
    """Test using a fixture."""
    assert sample_fixture == "test_data"  # noqa: S101


@pytest.mark.asyncio
async def test_async_sample() -> None:
    """Sample async test."""
    assert True  # noqa: S101
