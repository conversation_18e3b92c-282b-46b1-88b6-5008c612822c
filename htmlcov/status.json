{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.4", "globals": "f8ea6422586631ebe6f0c9a896016480", "files": {"z_145eef247bfb46b6___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5a2dc17ead558053___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_5a2dc17ead558053___init___py.html", "file": "src/application/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5a2dc17ead558053_exceptions_py": {"hash": "897278d10f36d5e07e74cac0add09e2b", "index": {"url": "z_5a2dc17ead558053_exceptions_py.html", "file": "src/application/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6bbfde952873f33___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_a6bbfde952873f33___init___py.html", "file": "src/application/interactors/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6bbfde952873f33_register_user_py": {"hash": "0dc49b6a8ee0dbb8cd9f81a9d6fa2535", "index": {"url": "z_a6bbfde952873f33_register_user_py.html", "file": "src/application/interactors/register_user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_31b7ecd603c7d0f0___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_31b7ecd603c7d0f0___init___py.html", "file": "src/application/interfaces/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5a2dc17ead558053_validators_py": {"hash": "cb12cf7fac54de48a34e37cb10d7eacc", "index": {"url": "z_5a2dc17ead558053_validators_py.html", "file": "src/application/validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aabc4828f4d558e6___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_aabc4828f4d558e6___init___py.html", "file": "src/domain/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_24f42fec43edda32___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_24f42fec43edda32___init___py.html", "file": "src/domain/entities/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_24f42fec43edda32_session_py": {"hash": "ea0cb46d4455ae20a1972eb5e5b059b9", "index": {"url": "z_24f42fec43edda32_session_py.html", "file": "src/domain/entities/session.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_24f42fec43edda32_user_py": {"hash": "138d978a84c71e4490a72bbe604c1b28", "index": {"url": "z_24f42fec43edda32_user_py.html", "file": "src/domain/entities/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aabc4828f4d558e6_exceptions_py": {"hash": "431447c48c0ce8cc0cfe339a1e4e70d1", "index": {"url": "z_aabc4828f4d558e6_exceptions_py.html", "file": "src/domain/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_91a8153e379ad568___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_91a8153e379ad568___init___py.html", "file": "src/domain/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_632b3cb742b5fe7c___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_632b3cb742b5fe7c___init___py.html", "file": "src/infrastructure/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7bf3dbaa4d895318___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_7bf3dbaa4d895318___init___py.html", "file": "src/infrastructure/adapters/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_632b3cb742b5fe7c_exceptions_py": {"hash": "317fc6cd590387b96eeb67b7934bf141", "index": {"url": "z_632b3cb742b5fe7c_exceptions_py.html", "file": "src/infrastructure/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f1512723eddbe79___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_5f1512723eddbe79___init___py.html", "file": "src/main/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f1512723eddbe79_app_py": {"hash": "1ddf4e59782b9747a2b6c3b0d2b649fd", "index": {"url": "z_5f1512723eddbe79_app_py.html", "file": "src/main/app.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f1512723eddbe79_config_py": {"hash": "78a4b431fbd0e37ad0039df676ebc07f", "index": {"url": "z_5f1512723eddbe79_config_py.html", "file": "src/main/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6a525547ddf83ae3___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_6a525547ddf83ae3___init___py.html", "file": "src/main/ioc/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6a525547ddf83ae3_main_py": {"hash": "e382585e03c07993b8226455f11e0737", "index": {"url": "z_6a525547ddf83ae3_main_py.html", "file": "src/main/ioc/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7f68e522c75420___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_8f7f68e522c75420___init___py.html", "file": "src/main/ioc/providers/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7f68e522c75420_core_py": {"hash": "0e4fa3e0914c820acf4f084a6c7d8c2a", "index": {"url": "z_8f7f68e522c75420_core_py.html", "file": "src/main/ioc/providers/core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7f68e522c75420_interactor_py": {"hash": "259b45d10156c05a16f191e90419467e", "index": {"url": "z_8f7f68e522c75420_interactor_py.html", "file": "src/main/ioc/providers/interactor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7f68e522c75420_service_py": {"hash": "d132c2ab9dea6302937b600dacca722f", "index": {"url": "z_8f7f68e522c75420_service_py.html", "file": "src/main/ioc/providers/service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0ccb07ab3633eaac___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_0ccb07ab3633eaac___init___py.html", "file": "src/presentation/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2b9cce477dd2081f___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_2b9cce477dd2081f___init___py.html", "file": "src/presentation/controllers/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2b9cce477dd2081f_auth_py": {"hash": "ccf846f60c96dce13caf636de9c9040e", "index": {"url": "z_2b9cce477dd2081f_auth_py.html", "file": "src/presentation/controllers/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2b9cce477dd2081f_main_py": {"hash": "01f8af0737ed9065dcaacb92ce1b2e3b", "index": {"url": "z_2b9cce477dd2081f_main_py.html", "file": "src/presentation/controllers/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0ccb07ab3633eaac_exceptions_py": {"hash": "dd32361380f70bbdc86f5691e1249c95", "index": {"url": "z_0ccb07ab3633eaac_exceptions_py.html", "file": "src/presentation/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6239f5988ddcf5ba___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_6239f5988ddcf5ba___init___py.html", "file": "src/presentation/middlewares/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6239f5988ddcf5ba_session_py": {"hash": "6c29eb9061d9a29cd68146f0752370b7", "index": {"url": "z_6239f5988ddcf5ba_session_py.html", "file": "src/presentation/middlewares/session.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}