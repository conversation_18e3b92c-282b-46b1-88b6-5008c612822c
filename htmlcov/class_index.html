<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.4">coverage.py v7.10.4</a>,
            created at 2025-08-21 09:56 +0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src/__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053___init___py.html">src/application/__init__.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t1">src/application/exceptions.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t1"><data value='ApplicationError'>ApplicationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t5">src/application/exceptions.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t5"><data value='UserAlreadyExistsError'>UserAlreadyExistsError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t10">src/application/exceptions.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t10"><data value='InvalidCredentialsError'>InvalidCredentialsError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t14">src/application/exceptions.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t14"><data value='InvalidPasswordError'>InvalidPasswordError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t18">src/application/exceptions.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t18"><data value='LogInError'>LogInError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t22">src/application/exceptions.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html#t22"><data value='AuthenticationRequiredError'>AuthenticationRequiredError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html">src/application/exceptions.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6bbfde952873f33___init___py.html">src/application/interactors/__init__.py</a></td>
                <td class="name left"><a href="z_a6bbfde952873f33___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6bbfde952873f33_register_user_py.html#t6">src/application/interactors/register_user.py</a></td>
                <td class="name left"><a href="z_a6bbfde952873f33_register_user_py.html#t6"><data value='RegisterUserRequest'>RegisterUserRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6bbfde952873f33_register_user_py.html#t12">src/application/interactors/register_user.py</a></td>
                <td class="name left"><a href="z_a6bbfde952873f33_register_user_py.html#t12"><data value='RegisterUserResponse'>RegisterUserResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6bbfde952873f33_register_user_py.html#t16">src/application/interactors/register_user.py</a></td>
                <td class="name left"><a href="z_a6bbfde952873f33_register_user_py.html#t16"><data value='RegisterUserInteractor'>RegisterUserInteractor</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6bbfde952873f33_register_user_py.html">src/application/interactors/register_user.py</a></td>
                <td class="name left"><a href="z_a6bbfde952873f33_register_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_31b7ecd603c7d0f0___init___py.html">src/application/interfaces/__init__.py</a></td>
                <td class="name left"><a href="z_31b7ecd603c7d0f0___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a2dc17ead558053_validators_py.html">src/application/validators.py</a></td>
                <td class="name left"><a href="z_5a2dc17ead558053_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aabc4828f4d558e6___init___py.html">src/domain/__init__.py</a></td>
                <td class="name left"><a href="z_aabc4828f4d558e6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_24f42fec43edda32___init___py.html">src/domain/entities/__init__.py</a></td>
                <td class="name left"><a href="z_24f42fec43edda32___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_24f42fec43edda32_session_py.html#t11">src/domain/entities/session.py</a></td>
                <td class="name left"><a href="z_24f42fec43edda32_session_py.html#t11"><data value='Session'>Session</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_24f42fec43edda32_session_py.html">src/domain/entities/session.py</a></td>
                <td class="name left"><a href="z_24f42fec43edda32_session_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_24f42fec43edda32_user_py.html#t11">src/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_24f42fec43edda32_user_py.html#t11"><data value='User'>User</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_24f42fec43edda32_user_py.html">src/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_24f42fec43edda32_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aabc4828f4d558e6_exceptions_py.html#t1">src/domain/exceptions.py</a></td>
                <td class="name left"><a href="z_aabc4828f4d558e6_exceptions_py.html#t1"><data value='DomainError'>DomainError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aabc4828f4d558e6_exceptions_py.html#t5">src/domain/exceptions.py</a></td>
                <td class="name left"><a href="z_aabc4828f4d558e6_exceptions_py.html#t5"><data value='UserNotActiveError'>UserNotActiveError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aabc4828f4d558e6_exceptions_py.html">src/domain/exceptions.py</a></td>
                <td class="name left"><a href="z_aabc4828f4d558e6_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91a8153e379ad568___init___py.html">src/domain/services/__init__.py</a></td>
                <td class="name left"><a href="z_91a8153e379ad568___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_632b3cb742b5fe7c___init___py.html">src/infrastructure/__init__.py</a></td>
                <td class="name left"><a href="z_632b3cb742b5fe7c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7bf3dbaa4d895318___init___py.html">src/infrastructure/adapters/__init__.py</a></td>
                <td class="name left"><a href="z_7bf3dbaa4d895318___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_632b3cb742b5fe7c_exceptions_py.html#t1">src/infrastructure/exceptions.py</a></td>
                <td class="name left"><a href="z_632b3cb742b5fe7c_exceptions_py.html#t1"><data value='InfrastructureError'>InfrastructureError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_632b3cb742b5fe7c_exceptions_py.html#t5">src/infrastructure/exceptions.py</a></td>
                <td class="name left"><a href="z_632b3cb742b5fe7c_exceptions_py.html#t5"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_632b3cb742b5fe7c_exceptions_py.html">src/infrastructure/exceptions.py</a></td>
                <td class="name left"><a href="z_632b3cb742b5fe7c_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f1512723eddbe79___init___py.html">src/main/__init__.py</a></td>
                <td class="name left"><a href="z_5f1512723eddbe79___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f1512723eddbe79_app_py.html">src/main/app.py</a></td>
                <td class="name left"><a href="z_5f1512723eddbe79_app_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html#t8">src/main/config.py</a></td>
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html#t8"><data value='BaseSettings'>BaseSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html#t16">src/main/config.py</a></td>
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html#t16"><data value='ApplicationConfig'>ApplicationConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html#t21">src/main/config.py</a></td>
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html#t21"><data value='SessionConfig'>SessionConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html#t31">src/main/config.py</a></td>
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html#t31"><data value='Config'>Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html">src/main/config.py</a></td>
                <td class="name left"><a href="z_5f1512723eddbe79_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6a525547ddf83ae3___init___py.html">src/main/ioc/__init__.py</a></td>
                <td class="name left"><a href="z_6a525547ddf83ae3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6a525547ddf83ae3_main_py.html">src/main/ioc/main.py</a></td>
                <td class="name left"><a href="z_6a525547ddf83ae3_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7f68e522c75420___init___py.html">src/main/ioc/providers/__init__.py</a></td>
                <td class="name left"><a href="z_8f7f68e522c75420___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7f68e522c75420_core_py.html#t6">src/main/ioc/providers/core.py</a></td>
                <td class="name left"><a href="z_8f7f68e522c75420_core_py.html#t6"><data value='ConfigProvider'>ConfigProvider</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7f68e522c75420_core_py.html">src/main/ioc/providers/core.py</a></td>
                <td class="name left"><a href="z_8f7f68e522c75420_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7f68e522c75420_interactor_py.html#t6">src/main/ioc/providers/interactor.py</a></td>
                <td class="name left"><a href="z_8f7f68e522c75420_interactor_py.html#t6"><data value='InteractorProvider'>InteractorProvider</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7f68e522c75420_interactor_py.html">src/main/ioc/providers/interactor.py</a></td>
                <td class="name left"><a href="z_8f7f68e522c75420_interactor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7f68e522c75420_service_py.html#t4">src/main/ioc/providers/service.py</a></td>
                <td class="name left"><a href="z_8f7f68e522c75420_service_py.html#t4"><data value='ServiceProvider'>ServiceProvider</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7f68e522c75420_service_py.html">src/main/ioc/providers/service.py</a></td>
                <td class="name left"><a href="z_8f7f68e522c75420_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ccb07ab3633eaac___init___py.html">src/presentation/__init__.py</a></td>
                <td class="name left"><a href="z_0ccb07ab3633eaac___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2b9cce477dd2081f___init___py.html">src/presentation/controllers/__init__.py</a></td>
                <td class="name left"><a href="z_2b9cce477dd2081f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2b9cce477dd2081f_auth_py.html">src/presentation/controllers/auth.py</a></td>
                <td class="name left"><a href="z_2b9cce477dd2081f_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2b9cce477dd2081f_main_py.html">src/presentation/controllers/main.py</a></td>
                <td class="name left"><a href="z_2b9cce477dd2081f_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ccb07ab3633eaac_exceptions_py.html">src/presentation/exceptions.py</a></td>
                <td class="name left"><a href="z_0ccb07ab3633eaac_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6239f5988ddcf5ba___init___py.html">src/presentation/middlewares/__init__.py</a></td>
                <td class="name left"><a href="z_6239f5988ddcf5ba___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6239f5988ddcf5ba_session_py.html#t9">src/presentation/middlewares/session.py</a></td>
                <td class="name left"><a href="z_6239f5988ddcf5ba_session_py.html#t9"><data value='SessionMiddleware'>SessionMiddleware</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6239f5988ddcf5ba_session_py.html">src/presentation/middlewares/session.py</a></td>
                <td class="name left"><a href="z_6239f5988ddcf5ba_session_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>190</td>
                <td>190</td>
                <td>0</td>
                <td class="right" data-ratio="0 190">0%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.4">coverage.py v7.10.4</a>,
            created at 2025-08-21 09:56 +0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
