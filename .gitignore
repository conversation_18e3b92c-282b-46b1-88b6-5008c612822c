### Python template
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# sqlite extensions
*.db
*.sqlite3
*.sqlite

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Translations
*.mo
*.pot


# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific ioc or ioc
#   having no cross-platform support, pipenv may install ioc that don't work, or not
#   install all needed ioc.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-O<PERSON>onnor/pyflow
__pypackages__/

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Pyre type checker
.pyre/
.idea/
.env
cache/
.aider*